'use client';

import React, { useState } from 'react';
import { Download, Loader2, ExternalLink } from 'lucide-react';
import { extractFilenameFromUrl } from '@/utils/arabic-url-utils';

interface PDFViewerProps {
  url: string;
  title?: string;
  onDownload?: (url: string) => void;
  isMobile?: boolean;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const PDFViewer: React.FC<PDFViewerProps> = ({ url, title, onDownload, isMobile: _isMobile }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Process the URL for direct PDF display
  const getViewerUrl = (pdfUrl: string): string => {
    // For Google Drive files, use direct preview format
    if (pdfUrl.includes('drive.google.com/file/d/')) {
      const fileIdMatch = pdfUrl.match(/\/d\/([^\/]+)/);
      if (fileIdMatch && fileIdMatch[1]) {
        const fileId = fileIdMatch[1];
        return `https://drive.google.com/file/d/${fileId}/preview`;
      }
    }

    // For all other URLs, return the direct URL for native PDF display
    return pdfUrl;
  };

  // Handle iframe load events
  const handleIframeLoad = () => {
    setLoading(false);
  };

  const handleIframeError = () => {
    setError('فشل تحميل ملف PDF. يرجى المحاولة مرة أخرى.');
    setLoading(false);
  };

  // Handle download
  const handleDownload = async () => {
    if (onDownload) {
      onDownload(url);
      return;
    }

    try {
      console.log('PDF Viewer - Starting download for:', url);

      // Special handling for Google Drive files
      if (url.includes('drive.google.com/file/d/')) {
        // Extract the file ID from the URL
        const fileIdMatch = url.match(/\/d\/([^\/]+)/);
        if (fileIdMatch && fileIdMatch[1]) {
          const fileId = fileIdMatch[1];

          // For Google Drive files, we'll use a direct link approach
          // This works better than trying to fetch and download
          const directDownloadUrl = `https://drive.google.com/uc?export=download&id=${fileId}`;

          console.log('Using direct Google Drive download link:', directDownloadUrl);

          // Create an invisible iframe to trigger the download
          const iframe = document.createElement('iframe');
          iframe.style.display = 'none';
          document.body.appendChild(iframe);

          // Set the iframe source to the download URL
          iframe.src = directDownloadUrl;

          // Remove the iframe after a delay
          setTimeout(() => {
            document.body.removeChild(iframe);
          }, 5000);

          // Also open in a new tab as a fallback
          window.open(directDownloadUrl, '_blank');
          return;
        }
      }

      // For non-Google Drive files, try multiple approaches
      console.log('Attempting to download non-Google Drive PDF:', url);

      try {
        // Try XMLHttpRequest approach which works better for some servers
        const xhr = new XMLHttpRequest();
        xhr.open('GET', url, true);
        xhr.responseType = 'blob';

        // Set up progress and completion handlers
        xhr.onload = function() {
          if (this.status === 200) {
            console.log('PDF fetched successfully via XHR');
            const blob = this.response;

            // Create a download link
            const blobUrl = URL.createObjectURL(blob);
            const link = document.createElement('a');

            // Extract filename from URL using the utility function
            const filename = extractFilenameFromUrl(url, 'مستند');

            console.log('Triggering download for PDF:', filename);

            // Set up and trigger download
            link.href = blobUrl;
            link.download = filename;
            link.target = '_blank';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Clean up the blob URL after a delay
            setTimeout(() => {
              URL.revokeObjectURL(blobUrl);
            }, 100);

            console.log('PDF download process completed');
          } else {
            throw new Error(`XHR error! status: ${this.status}`);
          }
        };

        xhr.onerror = function() {
          throw new Error('XHR network error');
        };

        xhr.send();
      } catch (xhrError) {
        console.error('XHR download failed, trying fetch method:', xhrError);

        // Fallback to fetch method
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/pdf',
            'Cache-Control': 'no-cache'
          },
          cache: 'no-store'
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const blob = await response.blob();

        if (blob.size === 0) {
          throw new Error('Downloaded file is empty');
        }

        // Create a download link
        const blobUrl = URL.createObjectURL(blob);
        const link = document.createElement('a');

        // Extract filename from URL using the utility function
        const filename = extractFilenameFromUrl(url, 'مستند');

        // Set up and trigger download
        link.href = blobUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(blobUrl);
      }
    } catch (error) {
      console.error('Error in PDF download process:', error);

      // Last resort: just open the file in a new tab
      console.log('All download methods failed, opening in new tab');
      window.open(url, '_blank');

      alert('تم فتح ملف PDF في نافذة جديدة. يمكنك تحميله من هناك.');
    }
  };

  return (
    <div className="w-full h-full flex flex-col bg-background relative">
      {/* PDF Viewer Container - ملء الشاشة بالكامل */}
      <div className="flex-1 w-full relative overflow-hidden bg-muted/10">
        {/* Loading overlay */}
        {loading && (
          <div className="absolute inset-0 bg-background/90 backdrop-blur-sm flex items-center justify-center z-10">
            <div className="flex flex-col items-center gap-3">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <p className="text-sm text-muted-foreground">جاري تحميل PDF...</p>
            </div>
          </div>
        )}

        {/* Error state */}
        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-background/95 backdrop-blur-sm z-20">
            <div className="text-center p-6 max-w-md mx-auto">
              <div className="bg-destructive/10 p-6 rounded-lg mb-4 w-full">
                <p className="text-destructive font-medium mb-2 text-lg">{error}</p>
                <p className="text-muted-foreground text-sm mb-4">
                  قد تكون هناك مشكلة في الاتصال بالإنترنت أو في الملف نفسه. يمكنك تجربة تحميل الملف مباشرة.
                </p>
              </div>

              <div className="flex flex-col gap-3 w-full">
                <button
                  onClick={handleDownload}
                  className="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md text-sm flex items-center justify-center gap-2 w-full"
                >
                  <Download className="h-4 w-4" />
                  <span>تحميل الملف</span>
                </button>

                <button
                  onClick={() => window.open(url, '_blank')}
                  className="bg-muted hover:bg-muted/80 text-muted-foreground px-4 py-2 rounded-md text-sm flex items-center justify-center gap-2 w-full"
                >
                  <ExternalLink className="h-4 w-4" />
                  <span>فتح في نافذة جديدة</span>
                </button>

                <button
                  onClick={() => window.location.reload()}
                  className="bg-muted hover:bg-muted/80 text-muted-foreground px-4 py-2 rounded-md text-sm flex items-center justify-center gap-2 w-full"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                    <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
                    <path d="M21 3v5h-5"></path>
                    <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
                    <path d="M3 21v-5h5"></path>
                  </svg>
                  <span>إعادة المحاولة</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* PDF iframe - عرض مباشر بدون Google Drive */}
        {!error && (
          <iframe
            src={getViewerUrl(url)}
            className="w-full h-full border-none bg-white"
            onLoad={handleIframeLoad}
            onError={handleIframeError}
            title={title || "PDF Viewer"}
            sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-downloads allow-top-navigation"
            referrerPolicy="no-referrer"
            loading="lazy"
            style={{
              minHeight: '100%',
              width: '100%',
              border: 'none',
              outline: 'none'
            }}
          />
        )}
      </div>

      {/* Controls - شريط تحكم مدمج في الأسفل */}
      <div className="w-full border-t bg-background/95 backdrop-blur-sm p-2 flex items-center justify-between shrink-0">
        <div className="text-xs text-muted-foreground truncate flex-1 mr-2">
          {title || 'مستند PDF'}
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={handleDownload}
            className="bg-primary text-primary-foreground hover:bg-primary/90 px-3 py-1.5 rounded-md text-xs flex items-center gap-1 transition-colors"
            title="تحميل الملف"
          >
            <Download className="h-3 w-3" />
            <span className="hidden sm:inline">تحميل</span>
          </button>

          <button
            onClick={() => window.open(url, '_blank')}
            className="bg-muted hover:bg-muted/80 text-muted-foreground px-3 py-1.5 rounded-md text-xs flex items-center gap-1 transition-colors"
            title="فتح في نافذة جديدة"
          >
            <ExternalLink className="h-3 w-3" />
            <span className="hidden sm:inline">فتح خارجياً</span>
          </button>
        </div>
      </div>
    </div>
  );
};
